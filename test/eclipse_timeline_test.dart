import 'package:astreal/models/astro_event.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Eclipse Timeline Tests', () {
    testWidgets('測試EclipseTimelineEntry功能', (WidgetTester tester) async {
      // 創建測試用的時間線條目
      final utcTime = DateTime.utc(2024, 3, 3, 8, 44, 25);
      final localTime = DateTime(2024, 3, 3, 16, 44, 25);
      
      final timelineEntry = EclipseTimelineEntry(
        phaseName: '半影食開始',
        utcTime: utcTime,
        localTime: localTime,
        visibility: VisibilityStatus.belowHorizon,
      );

      // 測試基本屬性
      expect(timelineEntry.phaseName, equals('半影食開始'));
      expect(timelineEntry.utcTime, equals(utcTime));
      expect(timelineEntry.localTime, equals(localTime));
      expect(timelineEntry.visibility, equals(VisibilityStatus.belowHorizon));

      // 測試可見性文字
      expect(timelineEntry.getVisibilityText(), equals('不，在地平線以下'));

      print('✅ EclipseTimelineEntry功能測試通過');
    });

    testWidgets('測試VisibilityStatus可見性狀態', (WidgetTester tester) async {
      // 測試各種可見性狀態的文字顯示
      final testCases = [
        {'status': VisibilityStatus.visible, 'expected': '是的'},
        {'status': VisibilityStatus.notVisible, 'expected': '不，在地平線以下'},
        {'status': VisibilityStatus.maybeVisible, 'expected': '也許，觸及地平線'},
        {'status': VisibilityStatus.belowHorizon, 'expected': '不，在地平線以下'},
        {'status': VisibilityStatus.touchingHorizon, 'expected': '也許，觸及地平線'},
      ];

      for (final testCase in testCases) {
        final entry = EclipseTimelineEntry(
          phaseName: '測試階段',
          utcTime: DateTime.now(),
          localTime: DateTime.now(),
          visibility: testCase['status'] as VisibilityStatus,
        );
        
        expect(entry.getVisibilityText(), equals(testCase['expected']));
      }

      print('✅ VisibilityStatus可見性狀態測試通過');
    });

    testWidgets('測試LunarEclipseDetails月食詳細信息', (WidgetTester tester) async {
      // 創建測試用的月食詳細信息
      final maximumTime = DateTime(2024, 3, 3, 19, 33, 46);
      final penumbralBeginTime = DateTime(2024, 3, 3, 16, 44, 25);
      final partialBeginTime = DateTime(2024, 3, 3, 17, 50, 7);
      final totalBeginTime = DateTime(2024, 3, 3, 19, 4, 34);
      final totalEndTime = DateTime(2024, 3, 3, 20, 2, 49);
      final partialEndTime = DateTime(2024, 3, 3, 21, 17, 15);
      final penumbralEndTime = DateTime(2024, 3, 3, 22, 23, 6);

      final lunarEclipseDetails = LunarEclipseDetails(
        maximumTime: maximumTime,
        penumbralBeginTime: penumbralBeginTime,
        partialBeginTime: partialBeginTime,
        totalBeginTime: totalBeginTime,
        totalEndTime: totalEndTime,
        partialEndTime: partialEndTime,
        penumbralEndTime: penumbralEndTime,
        eclipseType: EclipseType.lunarTotal,
        magnitude: 1.150,
        penumbralMagnitude: 2.184,
        isVisible: true,
        totalDuration: const Duration(hours: 5, minutes: 39),
        totalityDuration: const Duration(minutes: 58),
        partialDuration: const Duration(hours: 2, minutes: 29),
        penumbralDuration: const Duration(hours: 2, minutes: 12),
        description: '月全食，食分：115.0%',
      );

      // 測試基本屬性
      expect(lunarEclipseDetails.maximumTime, equals(maximumTime));
      expect(lunarEclipseDetails.eclipseType, equals(EclipseType.lunarTotal));
      expect(lunarEclipseDetails.magnitude, equals(1.150));
      expect(lunarEclipseDetails.penumbralMagnitude, equals(2.184));
      expect(lunarEclipseDetails.isVisible, isTrue);

      // 測試getAllPhases方法
      final phases = lunarEclipseDetails.getAllPhases();
      expect(phases[LunarEclipsePhase.penumbralBegin], equals(penumbralBeginTime));
      expect(phases[LunarEclipsePhase.partialBegin], equals(partialBeginTime));
      expect(phases[LunarEclipsePhase.totalBegin], equals(totalBeginTime));
      expect(phases[LunarEclipsePhase.maximum], equals(maximumTime));
      expect(phases[LunarEclipsePhase.totalEnd], equals(totalEndTime));
      expect(phases[LunarEclipsePhase.partialEnd], equals(partialEndTime));
      expect(phases[LunarEclipsePhase.penumbralEnd], equals(penumbralEndTime));

      print('✅ LunarEclipseDetails月食詳細信息測試通過');
    });

    testWidgets('測試LunarEclipsePhase階段名稱', (WidgetTester tester) async {
      // 測試各個月食階段的中文名稱
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.penumbralBegin), equals('半影食開始'));
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.partialBegin), equals('偏食開始'));
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.totalBegin), equals('全食開始'));
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.maximum), equals('食甚'));
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.totalEnd), equals('全食結束'));
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.partialEnd), equals('偏食結束'));
      expect(LunarEclipseDetails.getLunarPhaseName(LunarEclipsePhase.penumbralEnd), equals('半影食結束'));

      print('✅ LunarEclipsePhase階段名稱測試通過');
    });

    testWidgets('測試EclipseStatistics統計數據', (WidgetTester tester) async {
      // 創建測試用的統計數據
      final statistics = EclipseStatistics(
        magnitude: 1.150,
        obscuration: 100.0,
        penumbralMagnitude: 2.184,
        totalDuration: const Duration(hours: 5, minutes: 39),
        totalityDuration: const Duration(minutes: 58),
        partialDuration: const Duration(hours: 2, minutes: 29),
        penumbralDuration: const Duration(hours: 2, minutes: 12),
      );

      // 測試統計數據屬性
      expect(statistics.magnitude, equals(1.150));
      expect(statistics.obscuration, equals(100.0));
      expect(statistics.penumbralMagnitude, equals(2.184));
      expect(statistics.totalDuration.inMinutes, equals(339)); // 5小時39分鐘
      expect(statistics.totalityDuration?.inMinutes, equals(58)); // 58分鐘
      expect(statistics.partialDuration?.inMinutes, equals(149)); // 2小時29分鐘
      expect(statistics.penumbralDuration?.inMinutes, equals(132)); // 2小時12分鐘

      print('✅ EclipseStatistics統計數據測試通過');
      print('總持續時間: ${statistics.totalDuration.inMinutes}分鐘');
      print('全食持續時間: ${statistics.totalityDuration?.inMinutes}分鐘');
      print('偏食持續時間: ${statistics.partialDuration?.inMinutes}分鐘');
      print('半影食持續時間: ${statistics.penumbralDuration?.inMinutes}分鐘');
    });

    testWidgets('測試月食時間線邏輯', (WidgetTester tester) async {
      // 測試月食時間線的邏輯順序
      final phases = [
        LunarEclipsePhase.penumbralBegin,
        LunarEclipsePhase.partialBegin,
        LunarEclipsePhase.totalBegin,
        LunarEclipsePhase.maximum,
        LunarEclipsePhase.totalEnd,
        LunarEclipsePhase.partialEnd,
        LunarEclipsePhase.penumbralEnd,
      ];

      // 驗證階段順序
      expect(phases.length, equals(7));
      expect(phases[0], equals(LunarEclipsePhase.penumbralBegin));
      expect(phases[3], equals(LunarEclipsePhase.maximum)); // 食甚在中間
      expect(phases[6], equals(LunarEclipsePhase.penumbralEnd));

      // 驗證對稱性（開始和結束階段）
      expect(phases[0].toString().contains('Begin'), isTrue);
      expect(phases[6].toString().contains('End'), isTrue);
      expect(phases[1].toString().contains('Begin'), isTrue);
      expect(phases[5].toString().contains('End'), isTrue);

      print('✅ 月食時間線邏輯測試通過');
      print('月食階段順序: ${phases.map((p) => LunarEclipseDetails.getLunarPhaseName(p)).join(' → ')}');
    });
  });
}
