import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';

import '../models/astro_event.dart';
import '../models/birth_data.dart';
import '../services/astro_calendar_service.dart';
import '../utils/LoggerUtils.dart';

/// 星象日曆頁面的ViewModel
class AstroCalendarViewModel extends ChangeNotifier {
  final AstroCalendarService _calendarService = AstroCalendarService();

  // 狀態變數
  bool _isLoading = false;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;

  // 事件數據
  Map<DateTime, List<AstroEvent>> _events = {};
  List<AstroEvent> _selectedDayEvents = [];

  // 篩選設置
  Set<AstroEventType> _selectedEventTypes = AstroEventType.values.toSet();

  // 緩存機制
  final Map<String, Map<DateTime, List<AstroEvent>>> _monthlyCache = {};
  DateTime? _lastLoadedMonth;

  // 位置設置
  double _latitude = 25.0;
  double _longitude = 121.0;
  BirthData? _natalPerson;

  // Getters
  bool get isLoading => _isLoading;
  DateTime get focusedDay => _focusedDay;
  DateTime get selectedDay => _selectedDay;
  CalendarFormat get calendarFormat => _calendarFormat;
  Map<DateTime, List<AstroEvent>> get events => _events;
  List<AstroEvent> get selectedDayEvents => _selectedDayEvents;
  Set<AstroEventType> get selectedEventTypes => _selectedEventTypes;
  double get latitude => _latitude;
  double get longitude => _longitude;
  BirthData? get natalPerson => _natalPerson;

  /// 設置載入狀態
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 設置焦點日期
  void setFocusedDay(DateTime day, {bool autoLoad = true}) {
    _focusedDay = day;
    notifyListeners();

    // 載入該月的事件（可選）
    if (autoLoad) {
      loadMonthlyEvents(day.year, day.month);
    }
  }

  /// 設置選中日期
  void setSelectedDay(DateTime day) {
    _selectedDay = day;
    _updateSelectedDayEvents();
    notifyListeners();
  }

  /// 設置日曆格式
  void setCalendarFormat(CalendarFormat format) {
    _calendarFormat = format;
    notifyListeners();
  }

  /// 設置位置
  void setLocation(double latitude, double longitude) {
    if (_latitude != latitude || _longitude != longitude) {
      _latitude = latitude;
      _longitude = longitude;

      // 清除緩存，因為位置改變了
      _clearCache();

      notifyListeners();

      // 重新載入當前月份的事件
      loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
    }
  }

  /// 設置本命盤人物
  void setNatalPerson(BirthData? person) {
    if (_natalPerson?.id != person?.id) {
      _natalPerson = person;

      // 清除緩存，因為人物改變了
      _clearCache();

      notifyListeners();

      // 重新載入當前月份的事件
      loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
    }
  }

  /// 切換事件類型篩選
  void toggleEventType(AstroEventType type) {
    if (_selectedEventTypes.contains(type)) {
      _selectedEventTypes.remove(type);
    } else {
      _selectedEventTypes.add(type);
    }
    _updateSelectedDayEvents();
    notifyListeners();
  }

  /// 載入指定月份的事件
  Future<void> loadMonthlyEvents(int year, int month) async {
    try {

      await Future.delayed(const Duration(seconds: 2)); // 模擬刷新
      // 檢查是否是同一個月，避免重複載入
      final currentMonth = DateTime(year, month);
      if (_lastLoadedMonth != null &&
          _lastLoadedMonth!.year == year &&
          _lastLoadedMonth!.month == month) {
        logger.d('月份 $year年$month月 已載入，跳過重複載入');
        setLoading(false); // 確保設置loading為false
        return;
      }
      setLoading(true);
      // 檢查緩存
      final cacheKey = '${_latitude}_${_longitude}_${_natalPerson?.id ?? 'default'}';

      if (_monthlyCache.containsKey(cacheKey)) {
        // 檢查該月份是否已緩存
        final cachedMonthData = <DateTime, List<AstroEvent>>{};
        for (final entry in _monthlyCache[cacheKey]!.entries) {
          if (entry.key.year == year && entry.key.month == month) {
            cachedMonthData[entry.key] = entry.value;
          }
        }

        if (cachedMonthData.isNotEmpty) {
          // 使用緩存數據
          _events = cachedMonthData;
          _updateSelectedDayEvents();
          _lastLoadedMonth = currentMonth;
          logger.i('從緩存載入 $year年$month月 星象事件: ${cachedMonthData.length} 天');
          setLoading(false); // 確保設置loading為false
          return;
        }
      }

      // 從服務載入數據
      final monthlyEvents = await _calendarService.getMonthlyEvents(
        year,
        month,
        latitude: _latitude,
        longitude: _longitude,
        natalPerson: _natalPerson,
      );

      // 將事件按日期分組
      final eventsMap = <DateTime, List<AstroEvent>>{};
      for (final event in monthlyEvents) {
        final eventDate = DateTime(
          event.dateTime.year,
          event.dateTime.month,
          event.dateTime.day,
        );

        if (eventsMap[eventDate] == null) {
          eventsMap[eventDate] = [];
        }
        eventsMap[eventDate]!.add(event);
      }

      _events = eventsMap;
      _updateSelectedDayEvents();
      _lastLoadedMonth = currentMonth;

      // 更新緩存
      if (!_monthlyCache.containsKey(cacheKey)) {
        _monthlyCache[cacheKey] = {};
      }
      _monthlyCache[cacheKey]!.addAll(eventsMap);

      // 限制緩存大小，只保留最近3個月的數據
      if (_monthlyCache[cacheKey]!.length > 93) { // 3個月 * 31天 = 93天
        final sortedKeys = _monthlyCache[cacheKey]!.keys.toList()
          ..sort((a, b) => b.compareTo(a));
        // 保留最新的93天數據
        final keysToRemove = sortedKeys.skip(93).toList();
        for (final key in keysToRemove) {
          _monthlyCache[cacheKey]!.remove(key);
        }
      }

      logger.i('載入 $year年$month月 星象事件完成: ${monthlyEvents.length} 個');
    } catch (e) {
      logger.e('載入月度星象事件失敗: $e');
    } finally {
      setLoading(false);
    }
  }

  /// 載入指定日期的事件
  Future<void> loadDailyEvents(DateTime date) async {
    try {
      final dailyEvents = await _calendarService.getDailyEvents(
        date,
        latitude: _latitude,
        longitude: _longitude,
        natalPerson: _natalPerson,
      );

      final eventDate = DateTime(date.year, date.month, date.day);
      _events[eventDate] = dailyEvents;

      if (isSameDay(date, _selectedDay)) {
        _updateSelectedDayEvents();
      }

      notifyListeners();
      logger.d('載入 ${date.toString().split(' ')[0]} 星象事件完成: ${dailyEvents.length} 個');
    } catch (e) {
      logger.e('載入日度星象事件失敗: $e');
    }
  }

  /// 獲取指定日期的事件
  List<AstroEvent> getEventsForDay(DateTime day) {
    final eventDate = DateTime(day.year, day.month, day.day);
    final dayEvents = _events[eventDate] ?? [];

    // 根據篩選設置過濾事件
    return dayEvents.where((event) => _selectedEventTypes.contains(event.type)).toList();
  }

  /// 更新選中日期的事件
  void _updateSelectedDayEvents() {
    _selectedDayEvents = getEventsForDay(_selectedDay);
  }

  /// 初始化
  Future<void> initialize() async {
    await loadMonthlyEvents(_focusedDay.year, _focusedDay.month);

    // 預載入相鄰月份（背景執行，不阻塞UI）
    preloadAdjacentMonths(_focusedDay.year, _focusedDay.month);
  }

  /// 刷新當前月份
  Future<void> refresh() async {
    // 清除當前月份的緩存
    final cacheKey = '${_latitude}_${_longitude}_${_natalPerson?.id ?? 'default'}';
    final monthKey = DateTime(_focusedDay.year, _focusedDay.month);
    if (_monthlyCache.containsKey(cacheKey)) {
      _monthlyCache[cacheKey]!.remove(monthKey);
    }

    await loadMonthlyEvents(_focusedDay.year, _focusedDay.month);
  }

  /// 設置事件數據（測試用）
  void setEvents(Map<DateTime, List<AstroEvent>> events) {
    _events = events;
    _updateSelectedDayEvents();
    notifyListeners();
  }

  /// 獲取事件類型的顯示名稱
  String getEventTypeDisplayName(AstroEventType type) {
    switch (type) {
      case AstroEventType.moonPhase:
        return '月相';
      case AstroEventType.seasonChange:
        return '節氣';
      case AstroEventType.planetAspect:
        return '相位';
      case AstroEventType.planetSignChange:
        return '換座';
      case AstroEventType.planetRetrograde:
        return '逆行';
      case AstroEventType.eclipse:
        return '蝕相';
    }
  }

  /// 獲取事件類型的圖標
  IconData getEventTypeIcon(AstroEventType type) {
    switch (type) {
      case AstroEventType.moonPhase:
        return Icons.brightness_4;
      case AstroEventType.seasonChange:
        return Icons.wb_sunny;
      case AstroEventType.planetAspect:
        return Icons.timeline;
      case AstroEventType.planetSignChange:
        return Icons.swap_horiz;
      case AstroEventType.planetRetrograde:
        return Icons.refresh;
      case AstroEventType.eclipse:
        return Icons.brightness_2;
    }
  }

  /// 獲取事件類型的顏色
  Color getEventTypeColor(AstroEventType type) {
    switch (type) {
      case AstroEventType.moonPhase:
        return Colors.indigo;
      case AstroEventType.seasonChange:
        return Colors.green;
      case AstroEventType.planetAspect:
        return Colors.orange;
      case AstroEventType.planetSignChange:
        return Colors.blue;
      case AstroEventType.planetRetrograde:
        return Colors.red;
      case AstroEventType.eclipse:
        return Colors.purple;
    }
  }

  /// 清除緩存
  void _clearCache() {
    _monthlyCache.clear();
    _lastLoadedMonth = null;
    logger.d('已清除星象事件緩存');
  }

  /// 手動清除緩存（公開方法）
  void clearCache() {
    _clearCache();
    notifyListeners();
  }

  /// 預載入相鄰月份的事件（效能優化）
  Future<void> preloadAdjacentMonths(int year, int month) async {
    try {
      // 預載入上個月
      final prevMonth = month == 1 ? 12 : month - 1;
      final prevYear = month == 1 ? year - 1 : year;

      // 預載入下個月
      final nextMonth = month == 12 ? 1 : month + 1;
      final nextYear = month == 12 ? year + 1 : year;

      // 並行載入相鄰月份（不顯示loading）
      await Future.wait([
        _loadMonthlyEventsQuietly(prevYear, prevMonth),
        _loadMonthlyEventsQuietly(nextYear, nextMonth),
      ]);

      logger.d('預載入相鄰月份完成: ${prevYear}年${prevMonth}月, ${nextYear}年${nextMonth}月');
    } catch (e) {
      logger.w('預載入相鄰月份失敗: $e');
    }
  }

  /// 靜默載入月份事件（不顯示loading）
  Future<void> _loadMonthlyEventsQuietly(int year, int month) async {
    try {
      // 檢查緩存
      final cacheKey = '${_latitude}_${_longitude}_${_natalPerson?.id ?? 'default'}';
      final monthKey = DateTime(year, month);

      if (_monthlyCache.containsKey(cacheKey) &&
          _monthlyCache[cacheKey]!.containsKey(monthKey)) {
        return; // 已有緩存，跳過
      }

      // 從服務載入數據
      final monthlyEvents = await _calendarService.getMonthlyEvents(
        year,
        month,
        latitude: _latitude,
        longitude: _longitude,
        natalPerson: _natalPerson,
      );

      // 將事件按日期分組
      final eventsMap = <DateTime, List<AstroEvent>>{};
      for (final event in monthlyEvents) {
        final eventDate = DateTime(
          event.dateTime.year,
          event.dateTime.month,
          event.dateTime.day,
        );

        if (eventsMap[eventDate] == null) {
          eventsMap[eventDate] = [];
        }
        eventsMap[eventDate]!.add(event);
      }

      // 更新緩存
      if (!_monthlyCache.containsKey(cacheKey)) {
        _monthlyCache[cacheKey] = {};
      }
      _monthlyCache[cacheKey]!.addAll(eventsMap);

    } catch (e) {
      logger.w('靜默載入 $year年$month月 事件失敗: $e');
    }
  }
}
